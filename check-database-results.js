/**
 * Script untuk mengecek data yang tersimpan di database
 */

const { Sequelize } = require('./archive-service/node_modules/sequelize');

// Database configuration
const sequelize = new Sequelize('atma_db', 'atma_user', 'secret-passworrd', {
  host: 'localhost',
  port: 5432,
  dialect: 'postgres',
  logging: console.log
});

async function checkDatabaseResults() {
  try {
    console.log('🔍 Checking database results...');
    
    // Test connection
    await sequelize.authenticate();
    console.log('✅ Database connection successful');
    
    // Check analysis results
    const [results] = await sequelize.query(`
      SELECT 
        ar.id,
        ar.user_id,
        ar.status,
        ar.created_at,
        ar.updated_at,
        u.email as user_email,
        CASE 
          WHEN ar.assessment_data IS NOT NULL THEN 'Has assessment data'
          ELSE 'No assessment data'
        END as assessment_data_status,
        CASE 
          WHEN ar.persona_profile IS NOT NULL THEN 'Has persona profile'
          ELSE 'No persona profile'
        END as persona_profile_status
      FROM archive.analysis_results ar
      LEFT JOIN auth.users u ON ar.user_id = u.id
      ORDER BY ar.created_at DESC
      LIMIT 10
    `);
    
    console.log('\n📊 Recent Analysis Results:');
    console.log('='.repeat(80));
    
    if (results.length === 0) {
      console.log('❌ No analysis results found in database');
    } else {
      results.forEach((result, index) => {
        console.log(`\n${index + 1}. Result ID: ${result.id}`);
        console.log(`   User ID: ${result.user_id}`);
        console.log(`   User Email: ${result.user_email || 'Unknown'}`);
        console.log(`   Status: ${result.status}`);
        console.log(`   Assessment Data: ${result.assessment_data_status}`);
        console.log(`   Persona Profile: ${result.persona_profile_status}`);
        console.log(`   Created: ${result.created_at}`);
        console.log(`   Updated: ${result.updated_at}`);
      });
    }
    
    // Check specific result from the successful job
    console.log('\n🎯 Checking specific successful result...');
    const [specificResult] = await sequelize.query(`
      SELECT 
        ar.*,
        u.email as user_email
      FROM archive.analysis_results ar
      LEFT JOIN auth.users u ON ar.user_id = u.id
      WHERE ar.id = 'f072e96c-2546-4052-ab04-365c5014100d'
    `);
    
    if (specificResult.length > 0) {
      const result = specificResult[0];
      console.log('✅ Found successful result:');
      console.log(`   ID: ${result.id}`);
      console.log(`   User: ${result.user_email} (${result.user_id})`);
      console.log(`   Status: ${result.status}`);
      console.log(`   Created: ${result.created_at}`);
      
      if (result.assessment_data) {
        console.log('   Assessment Data Keys:', Object.keys(result.assessment_data));
      }
      
      if (result.persona_profile) {
        console.log('   Persona Profile Archetype:', result.persona_profile.archetype || 'Not specified');
      }
    } else {
      console.log('❌ Specific result not found');
    }
    
    // Count total results
    const [countResult] = await sequelize.query(`
      SELECT COUNT(*) as total_count
      FROM archive.analysis_results
    `);
    
    console.log(`\n📈 Total analysis results in database: ${countResult[0].total_count}`);
    
  } catch (error) {
    console.error('❌ Error checking database:', error.message);
  } finally {
    await sequelize.close();
  }
}

// Run the script
checkDatabaseResults();
