-- Initialize database for ATMA microservices
-- This script creates one database with separate schemas for each service

-- Create main database (skip if already exists)
-- CREATE DATABASE atma_db; -- Already created by POSTGRES_DB environment variable

-- User atma_user is already created by PostgreSQL environment variables
-- No need to create user again

-- Connect to main database
\c atma_db;

-- Create schemas for each service
CREATE SCHEMA auth;
CREATE SCHEMA archive;

-- Users table for Auth Service
CREATE TABLE auth.users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    token_balance INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON auth.users(email);
CREATE INDEX idx_users_created_at ON auth.users(created_at);

-- Analysis results table for Archive Service
CREATE TABLE archive.analysis_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    assessment_data JSONB,
    persona_profile JSONB NOT NULL,
    status VARCHAR(50) DEFAULT 'completed',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for better performance
CREATE INDEX idx_analysis_results_user_id ON archive.analysis_results(user_id);
CREATE INDEX idx_analysis_results_status ON archive.analysis_results(status);
CREATE INDEX idx_analysis_results_created_at ON archive.analysis_results(created_at);

-- Create GIN indexes for JSONB columns for better query performance
CREATE INDEX idx_analysis_results_assessment_data ON archive.analysis_results USING GIN (assessment_data);
CREATE INDEX idx_analysis_results_persona_profile ON archive.analysis_results USING GIN (persona_profile);

-- Add foreign key constraint between schemas
ALTER TABLE archive.analysis_results
ADD CONSTRAINT fk_analysis_results_user_id
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Insert sample data for testing (optional)

-- Sample user for testing
INSERT INTO auth.users (email, password_hash, token_balance) VALUES
('<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.G', 5);
-- Password is 'password123'

-- Sample analysis result for testing
INSERT INTO archive.analysis_results (user_id, assessment_data, persona_profile, status) VALUES
(
    (SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1),
    '{
        "riasec": {
            "realistic": 75,
            "investigative": 85,
            "artistic": 60,
            "social": 50,
            "enterprising": 70,
            "conventional": 55
        },
        "ocean": {
            "openness": 80,
            "conscientiousness": 65,
            "extraversion": 55,
            "agreeableness": 45,
            "neuroticism": 30
        },
        "viaIs": {
            "creativity": 85,
            "curiosity": 78,
            "judgment": 70,
            "loveOfLearning": 82,
            "perspective": 60,
            "bravery": 55,
            "perseverance": 68,
            "honesty": 73,
            "zest": 66,
            "love": 80,
            "kindness": 75,
            "socialIntelligence": 65,
            "teamwork": 60,
            "fairness": 70,
            "leadership": 67,
            "forgiveness": 58,
            "humility": 62,
            "prudence": 69,
            "selfRegulation": 61,
            "appreciationOfBeauty": 50,
            "gratitude": 72,
            "hope": 77,
            "humor": 65,
            "spirituality": 55
        }
    }',
    '[{
        "archetype": "The Innovator",
        "shortSummary": "A creative problem-solver with strong analytical skills and a passion for innovation.",
        "strengths": ["Creative thinking", "Problem solving", "Analytical skills", "Adaptability"],
        "weakness": ["Impatience", "Perfectionism", "Overthinking", "Difficulty with routine"],
        "careerRecommendation": [
            {
                "career": "Software Engineer",
                "reason": "Combines analytical thinking with creative problem-solving"
            },
            {
                "career": "Data Scientist",
                "reason": "Leverages analytical skills and pattern recognition"
            }
        ],
        "insights": [
            "Focus on developing patience for long-term projects",
            "Embrace structured approaches to balance creativity",
            "Seek environments that value innovation"
        ],
        "workEnvironment": "Dynamic, collaborative environment with autonomy and creative freedom",
        "roleModel": ["Steve Jobs", "Elon Musk", "Marie Curie"]
    }]',
    'completed'
);

-- Grant necessary permissions to atma_user
GRANT ALL PRIVILEGES ON DATABASE atma_db TO atma_user;
GRANT USAGE ON SCHEMA auth TO atma_user;
GRANT USAGE ON SCHEMA archive TO atma_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA auth TO atma_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA archive TO atma_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA auth TO atma_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA archive TO atma_user;
