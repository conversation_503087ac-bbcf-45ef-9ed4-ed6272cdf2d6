{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-15 16:44:32:4432"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-15 16:45:35:4535"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-15 16:49:39:4939"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-15 18:23:43:2343"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-15 18:49:06:496"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-15 19:02:14:214"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-15 19:04:12:412"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-16 03:57:46:5746"}
{"database":"atma_db","error":"connect ECONNREFUSED **********:5432","host":"postgres","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-15 21:50:36:5036"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-16 08:21:26:2126"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-16 13:16:18:1618"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-16 13:18:03:183"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-16 13:20:59:2059"}
{"error":"listen EADDRINUSE: address already in use :::3002","level":"error","message":"Uncaught exception","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1940:16)\n    at listenInCluster (node:net:1997:12)\n    at Server.listen (node:net:2102:7)\n    at Function.listen (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\express\\lib\\application.js:635:24)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\app.js:132:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-16 23:45:41:4541"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Failed to create analysis result","timestamp":"2025-07-16 23:50:28:5028","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:50:28:5028"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Failed to create analysis result","timestamp":"2025-07-16 23:51:15:5115","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:51:15:5115"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Failed to create analysis result","timestamp":"2025-07-16 23:53:02:532","userId":"550e8400-e29b-41d4-a716-446655440000"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:53:02:532"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Failed to create analysis result","timestamp":"2025-07-16 23:54:53:5453","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:54:53:5453"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Failed to create analysis result","timestamp":"2025-07-16 23:54:58:5458","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:54:58:5458"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Failed to create analysis result","timestamp":"2025-07-16 23:55:08:558","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:55:08:558"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Failed to create analysis result","timestamp":"2025-07-16 23:55:28:5528","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:55:28:5528"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Failed to create analysis result","timestamp":"2025-07-16 23:55:47:5547","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:55:47:5547"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:02:562","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:02:562"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:22:5622","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:22:5622"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:28:5628","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:28:5628"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:39:5639","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:39:5639"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:44:5644","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:44:5644"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:54:5654","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:54:5654"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:57:14:5714","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:57:14:5714"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:57:34:5734","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:57:35:5735"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:57:40:5740","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:57:40:5740"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:57:50:5750","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:57:50:5750"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:03:583","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:03:583"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:10:5810","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:10:5810"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:28:5828","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:28:5828"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:33:5833","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:33:5833"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:43:5843","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:43:5843"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:46:5846","userId":"550e8400-e29b-41d4-a716-446655440000"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:46:5846"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:03:593","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:03:593"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:22:5922","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:22:5922"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:27:5927","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:27:5927"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:37:5937","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:37:5937"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:57:5957","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:57:5957"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:35:25:3525","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:35:25:3525"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:35:30:3530","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:35:30:3530"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:35:40:3540","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:35:40:3540"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:36:00:360","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:36:00:360"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:36:40:3640","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:36:40:3640"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:36:45:3645","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:36:45:3645"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:36:55:3655","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:36:55:3655"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:37:15:3715","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:37:15:3715"}
{"error":"listen EADDRINUSE: address already in use :::3002","level":"error","message":"Uncaught exception","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1940:16)\n    at listenInCluster (node:net:1997:12)\n    at Server.listen (node:net:2102:7)\n    at Function.listen (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\express\\lib\\application.js:635:24)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\app.js:132:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-17 11:01:40:140"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:03:05:35","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:03:05:35"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:03:10:310","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:03:10:310"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:03:20:320","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:03:20:320"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:03:40:340","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:03:40:340"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:04:05:45","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:04:05:45"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:04:10:410","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:04:10:410"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:13:57:1357","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:13:57:1357"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:02:142","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:02:142"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:12:1412","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:12:1412"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:32:1432","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:32:1432"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:41:1441","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:41:1441"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:46:1446","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:46:1446"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:56:1456","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:56:1456"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:00:150","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:00:150"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:05:155","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:05:155"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:15:1515","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:15:1515"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:35:1535","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:35:1535"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:39:1539","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:39:1539"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:44:1544","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:44:1544"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:54:1554","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:54:1554"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:14:1614","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:14:1614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:14:1614","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:14:1614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:19:1619","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:19:1619"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:29:1629","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:29:1629"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:42:1642","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:42:1642"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:47:1647","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:47:1647"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:49:1649","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:49:1649"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:57:1657","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:16:57:1657"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:30:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:17:57:1757","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:30:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:17:57:1757"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:30:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:18:02:182","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:30:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:18:02:182"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:30:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:18:12:1812","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:30:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:18:12:1812"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:30:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:18:32:1832","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:30:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:18:32:1832"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:30:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:19:31:1931","userId":"550e8400-e29b-41d4-a716-446655440001"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:30:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:19:31:1931"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Database insert error details:","original":{"code":"23503","constraint":"fk_analysis_results_user_id","detail":"Key (user_id)=(550e8400-e29b-41d4-a716-446655440001) is not present in table \"users\".","file":"ri_triggers.c","length":318,"line":"2610","name":"error","parameters":["a946af89-d825-42d7-b4ed-0c906c076c53","550e8400-e29b-41d4-a716-446655440001","{\"riasec\":{\"realistic\":75,\"investigative\":85,\"artistic\":60,\"social\":50,\"enterprising\":70,\"conventional\":55},\"ocean\":{\"openness\":80,\"conscientiousness\":65,\"extraversion\":55,\"agreeableness\":45,\"neuroticism\":30},\"viaIs\":{\"creativity\":85,\"curiosity\":78,\"judgment\":70,\"loveOfLearning\":65,\"perspective\":75,\"bravery\":60,\"perseverance\":80,\"honesty\":90,\"zest\":70,\"love\":85,\"kindness\":88,\"socialIntelligence\":75,\"teamwork\":82,\"fairness\":85,\"leadership\":70,\"forgiveness\":75,\"humility\":65,\"prudence\":70,\"selfRegulation\":75,\"appreciationOfBeauty\":80,\"gratitude\":85,\"hope\":80,\"humor\":75,\"spirituality\":60}}","{\"archetype\":\"The Analytical Innovator\",\"shortSummary\":\"Test persona profile for debugging\",\"strengths\":[\"Analytical thinking\",\"Problem solving\",\"Creative innovation\"],\"weaknesses\":[\"Perfectionism\",\"Overthinking\",\"Impatience\"],\"careerRecommendation\":[{\"careerName\":\"Data Scientist\",\"careerProspect\":{\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"careerProgression\":\"high\",\"industryGrowth\":\"super high\",\"skillDevelopment\":\"high\"}},{\"careerName\":\"Software Engineer\",\"careerProspect\":{\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"skillDevelopment\":\"high\"}},{\"careerName\":\"Research Analyst\",\"careerProspect\":{\"jobAvailability\":\"moderate\",\"salaryPotential\":\"moderate\",\"careerProgression\":\"moderate\",\"industryGrowth\":\"moderate\",\"skillDevelopment\":\"high\"}}],\"insights\":[\"Focus on practical applications\",\"Develop communication skills\",\"Build leadership capabilities\"],\"workEnvironment\":\"Collaborative and innovative environment\",\"roleModel\":[\"Marie Curie\",\"Albert Einstein\",\"Steve Jobs\",\"Katherine Johnson\"]}","completed","2025-07-17 04:20:41.523 +00:00","2025-07-17 04:20:41.524 +00:00"],"routine":"ri_ReportViolation","schema":"archive","severity":"ERROR","sql":"INSERT INTO \"archive\".\"analysis_results\" (\"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7) RETURNING \"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\";","table":"analysis_results"},"parameters":["a946af89-d825-42d7-b4ed-0c906c076c53","550e8400-e29b-41d4-a716-446655440001","{\"riasec\":{\"realistic\":75,\"investigative\":85,\"artistic\":60,\"social\":50,\"enterprising\":70,\"conventional\":55},\"ocean\":{\"openness\":80,\"conscientiousness\":65,\"extraversion\":55,\"agreeableness\":45,\"neuroticism\":30},\"viaIs\":{\"creativity\":85,\"curiosity\":78,\"judgment\":70,\"loveOfLearning\":65,\"perspective\":75,\"bravery\":60,\"perseverance\":80,\"honesty\":90,\"zest\":70,\"love\":85,\"kindness\":88,\"socialIntelligence\":75,\"teamwork\":82,\"fairness\":85,\"leadership\":70,\"forgiveness\":75,\"humility\":65,\"prudence\":70,\"selfRegulation\":75,\"appreciationOfBeauty\":80,\"gratitude\":85,\"hope\":80,\"humor\":75,\"spirituality\":60}}","{\"archetype\":\"The Analytical Innovator\",\"shortSummary\":\"Test persona profile for debugging\",\"strengths\":[\"Analytical thinking\",\"Problem solving\",\"Creative innovation\"],\"weaknesses\":[\"Perfectionism\",\"Overthinking\",\"Impatience\"],\"careerRecommendation\":[{\"careerName\":\"Data Scientist\",\"careerProspect\":{\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"careerProgression\":\"high\",\"industryGrowth\":\"super high\",\"skillDevelopment\":\"high\"}},{\"careerName\":\"Software Engineer\",\"careerProspect\":{\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"skillDevelopment\":\"high\"}},{\"careerName\":\"Research Analyst\",\"careerProspect\":{\"jobAvailability\":\"moderate\",\"salaryPotential\":\"moderate\",\"careerProgression\":\"moderate\",\"industryGrowth\":\"moderate\",\"skillDevelopment\":\"high\"}}],\"insights\":[\"Focus on practical applications\",\"Develop communication skills\",\"Build leadership capabilities\"],\"workEnvironment\":\"Collaborative and innovative environment\",\"roleModel\":[\"Marie Curie\",\"Albert Einstein\",\"Steve Jobs\",\"Katherine Johnson\"]}","completed","2025-07-17 04:20:41.523 +00:00","2025-07-17 04:20:41.524 +00:00"],"sql":"INSERT INTO \"archive\".\"analysis_results\" (\"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7) RETURNING \"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\";","timestamp":"2025-07-17 11:20:41:2041"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:20:41:2041","userId":"550e8400-e29b-41d4-a716-446655440001"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:20:41:2041"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Database insert error details:","original":{"code":"23503","constraint":"fk_analysis_results_user_id","detail":"Key (user_id)=(550e8400-e29b-41d4-a716-446655440001) is not present in table \"users\".","file":"ri_triggers.c","length":318,"line":"2610","name":"error","parameters":["c9f7324a-f7f1-4f0d-80d2-d001cac0b287","550e8400-e29b-41d4-a716-446655440001","{\"riasec\":{\"realistic\":75,\"investigative\":85,\"artistic\":60,\"social\":50,\"enterprising\":70,\"conventional\":55},\"ocean\":{\"openness\":80,\"conscientiousness\":65,\"extraversion\":55,\"agreeableness\":45,\"neuroticism\":30},\"viaIs\":{\"creativity\":85,\"curiosity\":78,\"judgment\":70,\"loveOfLearning\":65,\"perspective\":75,\"bravery\":60,\"perseverance\":80,\"honesty\":90,\"zest\":70,\"love\":85,\"kindness\":88,\"socialIntelligence\":75,\"teamwork\":82,\"fairness\":85,\"leadership\":70,\"forgiveness\":75,\"humility\":65,\"prudence\":70,\"selfRegulation\":75,\"appreciationOfBeauty\":80,\"gratitude\":85,\"hope\":80,\"humor\":75,\"spirituality\":60}}","{\"archetype\":\"The Analytical Innovator\",\"shortSummary\":\"Test persona profile for debugging\",\"strengths\":[\"Analytical thinking\",\"Problem solving\",\"Creative innovation\"],\"weaknesses\":[\"Perfectionism\",\"Overthinking\",\"Impatience\"],\"careerRecommendation\":[{\"careerName\":\"Data Scientist\",\"careerProspect\":{\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"careerProgression\":\"high\",\"industryGrowth\":\"super high\",\"skillDevelopment\":\"high\"}},{\"careerName\":\"Software Engineer\",\"careerProspect\":{\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"skillDevelopment\":\"high\"}},{\"careerName\":\"Research Analyst\",\"careerProspect\":{\"jobAvailability\":\"moderate\",\"salaryPotential\":\"moderate\",\"careerProgression\":\"moderate\",\"industryGrowth\":\"moderate\",\"skillDevelopment\":\"high\"}}],\"insights\":[\"Focus on practical applications\",\"Develop communication skills\",\"Build leadership capabilities\"],\"workEnvironment\":\"Collaborative and innovative environment\",\"roleModel\":[\"Marie Curie\",\"Albert Einstein\",\"Steve Jobs\",\"Katherine Johnson\"]}","completed","2025-07-17 04:23:38.240 +00:00","2025-07-17 04:23:38.240 +00:00"],"routine":"ri_ReportViolation","schema":"archive","severity":"ERROR","sql":"INSERT INTO \"archive\".\"analysis_results\" (\"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7) RETURNING \"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\";","table":"analysis_results"},"parameters":["c9f7324a-f7f1-4f0d-80d2-d001cac0b287","550e8400-e29b-41d4-a716-446655440001","{\"riasec\":{\"realistic\":75,\"investigative\":85,\"artistic\":60,\"social\":50,\"enterprising\":70,\"conventional\":55},\"ocean\":{\"openness\":80,\"conscientiousness\":65,\"extraversion\":55,\"agreeableness\":45,\"neuroticism\":30},\"viaIs\":{\"creativity\":85,\"curiosity\":78,\"judgment\":70,\"loveOfLearning\":65,\"perspective\":75,\"bravery\":60,\"perseverance\":80,\"honesty\":90,\"zest\":70,\"love\":85,\"kindness\":88,\"socialIntelligence\":75,\"teamwork\":82,\"fairness\":85,\"leadership\":70,\"forgiveness\":75,\"humility\":65,\"prudence\":70,\"selfRegulation\":75,\"appreciationOfBeauty\":80,\"gratitude\":85,\"hope\":80,\"humor\":75,\"spirituality\":60}}","{\"archetype\":\"The Analytical Innovator\",\"shortSummary\":\"Test persona profile for debugging\",\"strengths\":[\"Analytical thinking\",\"Problem solving\",\"Creative innovation\"],\"weaknesses\":[\"Perfectionism\",\"Overthinking\",\"Impatience\"],\"careerRecommendation\":[{\"careerName\":\"Data Scientist\",\"careerProspect\":{\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"careerProgression\":\"high\",\"industryGrowth\":\"super high\",\"skillDevelopment\":\"high\"}},{\"careerName\":\"Software Engineer\",\"careerProspect\":{\"jobAvailability\":\"high\",\"salaryPotential\":\"high\",\"careerProgression\":\"high\",\"industryGrowth\":\"high\",\"skillDevelopment\":\"high\"}},{\"careerName\":\"Research Analyst\",\"careerProspect\":{\"jobAvailability\":\"moderate\",\"salaryPotential\":\"moderate\",\"careerProgression\":\"moderate\",\"industryGrowth\":\"moderate\",\"skillDevelopment\":\"high\"}}],\"insights\":[\"Focus on practical applications\",\"Develop communication skills\",\"Build leadership capabilities\"],\"workEnvironment\":\"Collaborative and innovative environment\",\"roleModel\":[\"Marie Curie\",\"Albert Einstein\",\"Steve Jobs\",\"Katherine Johnson\"]}","completed","2025-07-17 04:23:38.240 +00:00","2025-07-17 04:23:38.240 +00:00"],"sql":"INSERT INTO \"archive\".\"analysis_results\" (\"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7) RETURNING \"id\",\"user_id\",\"assessment_data\",\"persona_profile\",\"status\",\"created_at\",\"updated_at\";","timestamp":"2025-07-17 11:23:38:2338"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:23:38:2338","userId":"550e8400-e29b-41d4-a716-446655440001"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:32:16)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:23:38:2338"}
