{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-15 16:44:32:4432"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-15 16:45:35:4535"}
{"level":"warn","message":"Failed to connect to database, but continuing in development mode","timestamp":"2025-07-15 16:45:35:4535"}
{"level":"warn","message":"Make sure to run the init-databases.sql script to set up the database","timestamp":"2025-07-15 16:45:35:4535"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-15 16:45:35:4535"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-15 16:45:35:4535","version":"1.0.0"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-15 16:49:39:4939"}
{"database":"disconnected","level":"info","message":"Health check performed","status":"unhealthy","timestamp":"2025-07-15 16:49:39:4939"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 18:23:43:2343"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-15 18:23:43:2343"}
{"level":"warn","message":"Continuing in development mode despite database error","timestamp":"2025-07-15 18:23:43:2343"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-15 18:23:43:2343"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-15 18:23:43:2343","version":"1.0.0"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 18:23:59:2359"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 18:23:59:2359"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-15 18:49:06:496"}
{"level":"warn","message":"Failed to connect to database, but continuing in development mode","timestamp":"2025-07-15 18:49:06:496"}
{"level":"warn","message":"Make sure to run the init-databases.sql script to set up the database","timestamp":"2025-07-15 18:49:06:496"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-15 18:49:06:496"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-15 18:49:06:496","version":"1.0.0"}
{"level":"info","message":"Received SIGINT, starting graceful shutdown","timestamp":"2025-07-15 18:49:15:4915"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 19:02:14:214"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-15 19:02:14:214"}
{"level":"warn","message":"Continuing in development mode despite database error","timestamp":"2025-07-15 19:02:14:214"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-15 19:02:14:214"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-15 19:02:14:214","version":"1.0.0"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 19:04:12:412"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-15 19:04:12:412"}
{"level":"warn","message":"Continuing in development mode despite database error","timestamp":"2025-07-15 19:04:12:412"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-15 19:04:12:412"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-15 19:04:12:412","version":"1.0.0"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 03:57:46:5746"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-16 03:57:46:5746"}
{"level":"warn","message":"Continuing in development mode despite database error","timestamp":"2025-07-16 03:57:46:5746"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-16 03:57:46:5746"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-16 03:57:46:5746","version":"1.0.0"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:48:59:4859"}
{"level":"info","message":"Database models synchronized","timestamp":"2025-07-15 21:48:59:4859"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-15 21:48:59:4859"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-15 21:48:59:4859"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-15 21:48:59:4859","version":"1.0.0"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:49:03:493"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:49:03:493"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:49:33:4933"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:49:33:4933"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:50:03:503"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:50:03:503"}
{"database":"atma_db","error":"connect ECONNREFUSED **********:5432","host":"postgres","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-15 21:50:36:5036"}
{"database":"disconnected","level":"info","message":"Health check performed","status":"unhealthy","timestamp":"2025-07-15 21:50:36:5036"}
{"level":"info","message":"Received SIGTERM, starting graceful shutdown","timestamp":"2025-07-15 21:50:52:5052"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:50:53:5053"}
{"level":"info","message":"Database models synchronized","timestamp":"2025-07-15 21:50:54:5054"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-15 21:50:54:5054"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-15 21:50:54:5054"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-15 21:50:54:5054","version":"1.0.0"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:50:58:5058"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:50:58:5058"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:51:28:5128"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:51:28:5128"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:51:58:5158"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:51:58:5158"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:52:28:5228"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:52:28:5228"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:52:58:5258"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:52:58:5258"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:53:28:5328"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:53:28:5328"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:53:58:5358"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:53:58:5358"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:54:01:541"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:54:01:541"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:54:28:5428"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:54:28:5428"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:54:58:5458"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:54:58:5458"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:55:28:5528"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:55:28:5528"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:55:55:5555"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:55:55:5555"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:55:58:5558"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:55:58:5558"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:56:29:5629"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:56:29:5629"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:56:59:5659"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:56:59:5659"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:57:19:5719"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:57:19:5719"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:57:29:5729"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:57:29:5729"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:57:59:5759"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:57:59:5759"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:58:29:5829"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:58:29:5829"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:58:59:5859"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:58:59:5859"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:59:29:5929"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:59:29:5929"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:59:59:5959"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:59:59:5959"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:00:29:029"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:00:29:029"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:00:59:059"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:00:59:059"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:01:29:129"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:01:29:129"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:01:59:159"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:01:59:159"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:02:29:229"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:02:29:229"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:02:59:259"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:02:59:259"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:03:30:330"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:03:30:330"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:04:00:40"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:04:00:40"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:04:30:430"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:04:30:430"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:05:00:50"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:05:00:50"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:05:30:530"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:05:30:530"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:06:00:60"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:06:00:60"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:06:30:630"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:06:30:630"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:07:00:70"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:07:00:70"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:07:30:730"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:07:30:730"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:08:00:80"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:08:00:80"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:08:30:830"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:08:30:830"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:09:00:90"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:09:00:90"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:09:30:930"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:09:30:930"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:10:01:101"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:10:01:101"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:10:31:1031"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:10:31:1031"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:11:01:111"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:11:01:111"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:11:31:1131"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:11:31:1131"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:12:01:121"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:12:01:121"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:12:31:1231"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:12:31:1231"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:13:01:131"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:13:01:131"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:13:31:1331"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:13:31:1331"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:14:01:141"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:14:01:141"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:14:31:1431"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:14:31:1431"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:15:01:151"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:15:01:151"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:15:31:1531"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:15:31:1531"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:16:01:161"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:16:01:161"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:16:31:1631"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:16:31:1631"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:17:02:172"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:17:02:172"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:17:32:1732"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:17:32:1732"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:18:02:182"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:18:02:182"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:18:32:1832"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:18:32:1832"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:19:02:192"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:19:02:192"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:19:32:1932"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:19:32:1932"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:20:02:202"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:20:02:202"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:20:32:2032"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:20:32:2032"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:21:02:212"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:21:02:212"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:21:32:2132"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:21:32:2132"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:22:02:222"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:22:02:222"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:22:32:2232"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:22:32:2232"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:23:02:232"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:23:02:232"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:23:33:2333"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:23:33:2333"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:24:03:243"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:24:03:243"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:24:33:2433"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:24:33:2433"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:25:03:253"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:25:03:253"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:25:33:2533"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:25:33:2533"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:26:03:263"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:26:03:263"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:26:33:2633"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:26:33:2633"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:27:03:273"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:27:03:273"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:27:33:2733"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:27:33:2733"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:28:03:283"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:28:03:283"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:28:33:2833"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:28:33:2833"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:29:03:293"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:29:03:293"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:29:34:2934"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:29:34:2934"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:30:04:304"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:30:04:304"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:30:34:3034"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:30:34:3034"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:31:04:314"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:31:04:314"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:31:34:3134"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:31:34:3134"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:31:59:3159"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:31:59:3159"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:32:04:324"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:32:04:324"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:32:34:3234"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:32:34:3234"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:33:04:334"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:33:04:334"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:33:34:3334"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:33:34:3334"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:34:04:344"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:34:04:344"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:34:34:3434"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:34:34:3434"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:35:04:354"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:35:04:354"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:35:35:3535"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:35:35:3535"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:36:05:365"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:36:05:365"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:36:35:3635"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:36:35:3635"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:37:05:375"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:37:05:375"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:37:35:3735"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:37:35:3735"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:38:05:385"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:38:05:385"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:38:35:3835"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:38:35:3835"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:39:05:395"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:39:05:395"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:39:35:3935"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:39:35:3935"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:40:05:405"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:40:05:405"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:40:35:4035"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:40:35:4035"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:41:05:415"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:41:05:415"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:41:36:4136"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:41:36:4136"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:42:06:426"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:42:06:426"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:42:36:4236"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:42:36:4236"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:43:06:436"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:43:06:436"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:43:36:4336"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:43:36:4336"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:44:06:446"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:44:06:446"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:44:36:4436"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:44:36:4436"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:45:06:456"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:45:06:456"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:45:36:4536"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:45:36:4536"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:46:06:466"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:46:06:466"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:46:36:4636"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:46:36:4636"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:47:07:477"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:47:07:477"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:47:37:4737"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:47:37:4737"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:48:07:487"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:48:07:487"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:48:37:4837"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:48:37:4837"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:49:07:497"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:49:07:497"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:49:37:4937"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:49:37:4937"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:50:07:507"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:50:07:507"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:50:37:5037"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:50:37:5037"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:51:07:517"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:51:07:517"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:51:37:5137"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:51:37:5137"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:52:07:527"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:52:07:527"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:52:38:5238"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:52:38:5238"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:53:08:538"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:53:08:538"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:53:38:5338"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:53:38:5338"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:54:08:548"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:54:08:548"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:54:38:5438"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:54:38:5438"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:55:08:558"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:55:08:558"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:55:38:5538"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:55:38:5538"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:56:08:568"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:56:08:568"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:56:38:5638"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:56:38:5638"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:57:08:578"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:57:08:578"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:57:39:5739"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:57:39:5739"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:58:09:589"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:58:09:589"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:58:39:5839"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:58:39:5839"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:59:09:599"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:59:09:599"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:59:39:5939"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:59:39:5939"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:00:09:09"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:00:09:09"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:00:39:039"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:00:39:039"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:01:09:19"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:01:09:19"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:01:39:139"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:01:39:139"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:02:09:29"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:02:09:29"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:02:39:239"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:02:39:239"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:03:09:39"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:03:09:39"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:03:39:339"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:03:39:339"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:04:10:410"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:04:10:410"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:04:40:440"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:04:40:440"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:05:10:510"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:05:10:510"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:05:40:540"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:05:40:540"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:06:10:610"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:06:10:610"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:06:40:640"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:06:40:640"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:07:10:710"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:07:10:710"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:07:40:740"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:07:40:740"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:08:10:810"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:08:10:810"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:08:40:840"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:08:40:840"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:09:10:910"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:09:10:910"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:09:41:941"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:09:41:941"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:10:11:1011"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:10:11:1011"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:10:41:1041"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:10:41:1041"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:11:11:1111"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:11:11:1111"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:11:41:1141"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:11:41:1141"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:12:11:1211"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:12:11:1211"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:12:41:1241"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:12:41:1241"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:13:11:1311"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:13:11:1311"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:13:41:1341"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:13:41:1341"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:14:11:1411"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:14:11:1411"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:14:41:1441"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:14:41:1441"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:15:11:1511"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:15:11:1511"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:15:42:1542"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:15:42:1542"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:16:12:1612"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:16:12:1612"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:16:42:1642"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:16:42:1642"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:17:12:1712"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:17:12:1712"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:17:42:1742"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:17:42:1742"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:18:13:1813"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:18:13:1813"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:18:43:1843"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:18:43:1843"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:19:13:1913"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:19:13:1913"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:19:43:1943"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:19:43:1943"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:20:14:2014"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:20:14:2014"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:20:44:2044"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:20:44:2044"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:21:14:2114"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:21:14:2114"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:21:44:2144"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:21:44:2144"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:22:14:2214"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:22:14:2214"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:22:44:2244"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:22:44:2244"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:23:14:2314"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:23:14:2314"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:23:44:2344"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:23:44:2344"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:24:14:2414"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:24:14:2414"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:24:45:2445"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:24:45:2445"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:25:15:2515"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:25:15:2515"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:25:45:2545"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:25:45:2545"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:26:15:2615"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:26:15:2615"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:26:45:2645"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:26:45:2645"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:27:15:2715"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:27:15:2715"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:27:45:2745"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:27:45:2745"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:28:15:2815"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:28:15:2815"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:28:45:2845"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:28:45:2845"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:29:16:2916"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:29:16:2916"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:29:46:2946"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:29:46:2946"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:30:16:3016"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:30:16:3016"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:30:46:3046"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:30:46:3046"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:31:16:3116"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:31:16:3116"}
{"level":"info","message":"Received SIGTERM, starting graceful shutdown","timestamp":"2025-07-15 23:31:42:3142"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:32:38:3238"}
{"level":"info","message":"Database models synchronized","timestamp":"2025-07-15 23:32:38:3238"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-15 23:32:38:3238"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-15 23:32:38:3238"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-15 23:32:38:3238","version":"1.0.0"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:32:42:3242"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:32:42:3242"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:33:12:3312"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:33:12:3312"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:33:42:3342"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:33:42:3342"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:34:12:3412"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:34:12:3412"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:34:42:3442"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:34:42:3442"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:35:12:3512"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:35:12:3512"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:35:42:3542"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:35:42:3542"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:36:12:3612"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:36:12:3612"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:36:43:3643"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:36:43:3643"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:37:13:3713"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:37:13:3713"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:37:43:3743"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:37:43:3743"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:38:13:3813"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:38:13:3813"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:38:43:3843"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:38:43:3843"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:39:13:3913"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:39:13:3913"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:39:43:3943"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:39:43:3943"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:40:13:4013"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:40:13:4013"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:40:43:4043"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:40:43:4043"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:41:13:4113"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:41:13:4113"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:41:43:4143"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:41:43:4143"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:42:13:4213"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:42:13:4213"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 08:21:26:2126"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-16 08:21:26:2126"}
{"level":"warn","message":"Continuing in development mode despite database error","timestamp":"2025-07-16 08:21:26:2126"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-16 08:21:26:2126"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-16 08:21:26:2126","version":"1.0.0"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 13:16:18:1618"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-16 13:16:18:1618"}
{"level":"warn","message":"Continuing in development mode despite database error","timestamp":"2025-07-16 13:16:18:1618"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-16 13:16:18:1618"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-16 13:16:18:1618","version":"1.0.0"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 13:18:03:183"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-16 13:18:03:183"}
{"level":"warn","message":"Continuing in development mode despite database error","timestamp":"2025-07-16 13:18:03:183"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-16 13:18:03:183"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-16 13:18:03:183","version":"1.0.0"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 13:20:59:2059"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-16 13:20:59:2059"}
{"level":"warn","message":"Continuing in development mode despite database error","timestamp":"2025-07-16 13:20:59:2059"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-16 13:20:59:2059"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-16 13:20:59:2059","version":"1.0.0"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 13:26:05:265"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-16 13:26:05:265"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-16 13:26:05:265"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-16 13:26:05:265","version":"1.0.0"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 13:26:33:2633","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 13:26:33:2633","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 13:26:33:2633","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 13:26:33:2633","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 13:26:33:2633","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 13:26:33:2633","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 13:28:54:2854","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 13:28:54:2854","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 13:28:54:2854","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 13:28:54:2854","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 13:28:54:2854","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 13:28:54:2854","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 14:41:41:4141"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-16 14:41:41:4141"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-16 14:41:41:4141"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-16 14:41:41:4141","version":"1.0.0"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 14:42:04:424","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 14:42:04:424","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 14:42:04:424","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 14:42:04:424","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 14:42:04:424","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 14:42:04:424","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 14:43:00:430","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 14:43:00:430","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 14:43:00:430","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 14:43:00:430","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 14:43:00:430","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 14:43:00:430","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 14:43:01:431"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-16 14:43:01:431"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 14:43:01:431"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-16 14:43:01:431"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 14:43:08:438"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-16 14:43:08:438"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 14:43:11:4311","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 14:43:11:4311","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 14:43:11:4311","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 14:43:11:4311","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 14:43:11:4311","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 14:43:11:4311","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 14:46:14:4614"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-16 14:46:14:4614"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-16 14:46:14:4614"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-16 14:46:14:4614","version":"1.0.0"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 14:46:44:4644","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 14:46:44:4644","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 14:46:44:4644","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 14:46:44:4644","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 14:46:44:4644","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 14:46:44:4644","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 14:46:57:4657","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 14:46:57:4657","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 14:46:57:4657","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 14:46:57:4657","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 14:46:57:4657","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 14:46:57:4657","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 15:05:23:523"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-16 15:05:23:523"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-16 15:05:23:523"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-16 15:05:23:523","version":"1.0.0"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 15:20:28:2028","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-16 15:20:28:2028","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 15:20:28:2028","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-16 15:20:28:2028","totalAnalyses":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 15:20:28:2028","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-16 15:20:28:2028","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 15:20:28:2028","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-16 15:20:28:2028","totalAnalyses":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 15:20:30:2030"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-16 15:20:30:2030"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 15:20:30:2030"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-16 15:20:30:2030"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 15:20:52:2052"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-16 15:20:52:2052"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 15:20:55:2055"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-16 15:20:55:2055"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 15:20:56:2056"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-16 15:20:56:2056"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 15:20:56:2056"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-16 15:20:56:2056"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 15:20:56:2056"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-16 15:20:56:2056"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 15:20:58:2058","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-16 15:20:58:2058","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 15:20:58:2058","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-16 15:20:58:2058","totalAnalyses":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 15:20:58:2058","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 15:20:58:2058","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-16 15:20:58:2058","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-16 15:20:58:2058","totalAnalyses":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 15:21:03:213","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-16 15:21:03:213","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 15:21:03:213","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-16 15:21:03:213","totalAnalyses":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 15:21:03:213","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-16 15:21:03:213","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 15:21:03:213","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-16 15:21:03:213","totalAnalyses":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 15:57:35:5735","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-16 15:57:35:5735","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 15:57:35:5735","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-16 15:57:35:5735","totalAnalyses":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 15:57:36:5736","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-16 15:57:36:5736","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 15:57:36:5736","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-16 15:57:36:5736","totalAnalyses":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 15:57:42:5742","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-16 15:57:42:5742","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 15:57:42:5742","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-16 15:57:42:5742","totalAnalyses":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 15:57:42:5742","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-16 15:57:42:5742","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 15:57:42:5742","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-16 15:57:42:5742","totalAnalyses":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 15:58:36:5836","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-16 15:58:36:5836","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-16 15:58:36:5836","totalAnalyses":0,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 15:58:36:5836","total":0,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-16 15:58:36:5836","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 15:58:36:5836","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 15:58:36:5836","total":0,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-16 15:58:36:5836","totalAnalyses":0,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 16:15:14:1514"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-16 16:15:14:1514"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 16:15:14:1514"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-16 16:15:14:1514"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 16:18:28:1828","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-16 16:18:28:1828","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 16:18:28:1828","total":0,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-16 16:18:28:1828","totalAnalyses":0,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 16:18:28:1828","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-16 16:18:28:1828","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 16:18:28:1828","total":0,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-16 16:18:28:1828","totalAnalyses":0,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 16:18:44:1844","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-16 16:18:44:1844","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 16:18:44:1844","total":0,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-16 16:18:44:1844","totalAnalyses":0,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 16:18:44:1844","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 16:18:44:1844","total":0,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-16 16:18:44:1844","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-16 16:18:44:1844","totalAnalyses":0,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 16:24:42:2442"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-16 16:24:42:2442"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 16:25:24:2524"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-16 16:25:24:2524"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 16:25:26:2526","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 16:25:26:2526","total":0,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 16:28:11:2811"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-16 16:28:11:2811"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 16:28:14:2814","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 16:28:14:2814","total":0,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 16:30:43:3043"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-16 16:30:43:3043"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 16:30:46:3046","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 16:30:46:3046","total":0,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 16:33:58:3358","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 16:33:59:3359","total":0,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-16 16:34:00:340","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-16 16:34:00:340","totalAnalyses":0,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Fetching user overview statistics","timestamp":"2025-07-16 16:34:01:341","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"User overview statistics fetched successfully","timestamp":"2025-07-16 16:34:01:341","totalAnalyses":0,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 16:34:02:342"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-16 16:34:02:342"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 16:34:02:342"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-16 16:34:02:342"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 18:58:51:5851","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 18:58:51:5851","total":0,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-16 18:58:52:5852","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-16 18:58:52:5852","totalAnalyses":0,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Fetching user overview statistics","timestamp":"2025-07-16 18:58:53:5853","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"User overview statistics fetched successfully","timestamp":"2025-07-16 18:58:53:5853","totalAnalyses":0,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 18:58:54:5854"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-16 18:58:54:5854"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 18:58:54:5854"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-16 18:58:54:5854"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 23:26:06:266"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-16 23:26:06:266"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-16 23:26:06:266"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-16 23:26:06:266","version":"1.0.0"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 23:26:45:2645"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-16 23:26:45:2645"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-16 23:26:45:2645"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-16 23:26:45:2645","version":"1.0.0"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:43:58:4358"}
{"error":["\"persona_profile\" must be an array"],"level":"warn","message":"Request body validation failed","method":"POST","path":"/results","timestamp":"2025-07-16 23:43:58:4358"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 23:45:41:4541"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:47:09:479"}
{"error":["\"user_id\" must be a valid GUID","\"persona_profile\" must be an array"],"level":"warn","message":"Request body validation failed","method":"POST","path":"/results","timestamp":"2025-07-16 23:47:09:479"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:48:48:4848"}
{"error":["\"persona_profile\" must be an array"],"level":"warn","message":"Request body validation failed","method":"POST","path":"/results","timestamp":"2025-07-16 23:48:48:4848"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:49:06:496"}
{"error":["\"persona_profile\" must be an array"],"level":"warn","message":"Request body validation failed","method":"POST","path":"/results","timestamp":"2025-07-16 23:49:06:496"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:49:23:4923"}
{"error":["\"persona_profile\" must be an array"],"level":"warn","message":"Request body validation failed","method":"POST","path":"/results","timestamp":"2025-07-16 23:49:23:4923"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 23:49:50:4950"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-16 23:49:50:4950"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-16 23:49:50:4950"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-16 23:49:50:4950","version":"1.0.0"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:50:16:5016"}
{"error":["\"persona_profile.careerRecommendation\" must contain at least 3 items"],"level":"warn","message":"Request body validation failed","method":"POST","path":"/results","timestamp":"2025-07-16 23:50:16:5016"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:50:28:5028"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:50:28:5028","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Failed to create analysis result","timestamp":"2025-07-16 23:50:28:5028","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:50:28:5028"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:51:15:5115"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:51:15:5115","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Failed to create analysis result","timestamp":"2025-07-16 23:51:15:5115","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:51:15:5115"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 23:51:29:5129"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-16 23:51:29:5129"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-16 23:51:29:5129"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-16 23:51:29:5129","version":"1.0.0"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 23:52:15:5215"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-16 23:52:15:5215"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:52:24:5224"}
{"error":["\"persona_profile.shortSummary\" is required","\"persona_profile.strengths\" is required","\"persona_profile.weaknesses\" is required","\"persona_profile.careerRecommendation\" is required","\"persona_profile.insights\" is required","\"persona_profile.workEnvironment\" is required","\"persona_profile.roleModel\" is required"],"level":"warn","message":"Request body validation failed","method":"POST","path":"/results","timestamp":"2025-07-16 23:52:24:5224"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:53:02:532"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:53:02:532","userId":"550e8400-e29b-41d4-a716-446655440000"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Failed to create analysis result","timestamp":"2025-07-16 23:53:02:532","userId":"550e8400-e29b-41d4-a716-446655440000"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:53:02:532"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:54:53:5453"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:54:53:5453","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Failed to create analysis result","timestamp":"2025-07-16 23:54:53:5453","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:54:53:5453"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:54:58:5458"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:54:58:5458","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Failed to create analysis result","timestamp":"2025-07-16 23:54:58:5458","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:54:58:5458"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:55:08:558"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:55:08:558","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Failed to create analysis result","timestamp":"2025-07-16 23:55:08:558","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:55:08:558"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:55:28:5528"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:55:28:5528","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Failed to create analysis result","timestamp":"2025-07-16 23:55:28:5528","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:55:28:5528"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:55:47:5547"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:55:47:5547","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Failed to create analysis result","timestamp":"2025-07-16 23:55:47:5547","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:55:47:5547"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 23:55:58:5558"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-16 23:55:58:5558"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-16 23:55:58:5558"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-16 23:55:58:5558","version":"1.0.0"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:56:02:562"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:56:02:562","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:02:562","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:02:562"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:56:22:5622"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:56:22:5622","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:22:5622","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:22:5622"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:56:28:5628"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:56:28:5628","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:28:5628","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:28:5628"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:56:39:5639"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:56:39:5639","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:39:5639","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:39:5639"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:56:44:5644"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:56:44:5644","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:44:5644","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:44:5644"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:56:54:5654"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:56:54:5654","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:54:5654","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:56:54:5654"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:57:14:5714"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:57:14:5714","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:57:14:5714","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:57:14:5714"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 23:57:34:5734"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-16 23:57:34:5734"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-16 23:57:34:5734"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-16 23:57:34:5734","version":"1.0.0"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:57:34:5734"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:57:34:5734","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:57:34:5734","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:57:35:5735"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:57:40:5740"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:57:40:5740","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:57:40:5740","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:57:40:5740"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:57:50:5750"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:57:50:5750","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:57:50:5750","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:57:50:5750"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:58:03:583"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:58:03:583","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:03:583","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:03:583"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:58:10:5810"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:58:10:5810","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:10:5810","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:10:5810"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:58:28:5828"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:58:28:5828","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:28:5828","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:28:5828"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:58:33:5833"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:58:33:5833","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:33:5833","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:33:5833"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:58:43:5843"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:58:43:5843","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:43:5843","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:43:5843"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:58:46:5846"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:58:46:5846","userId":"550e8400-e29b-41d4-a716-446655440000"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:46:5846","userId":"550e8400-e29b-41d4-a716-446655440000"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:58:46:5846"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:59:03:593"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:59:03:593","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:03:593","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:03:593"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:59:22:5922"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:59:22:5922","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:22:5922","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:22:5922"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:59:27:5927"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:59:27:5927","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:27:5927","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:27:5927"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:59:37:5937"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:59:37:5937","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:37:5937","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:37:5937"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 23:59:52:5952"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-16 23:59:52:5952"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-16 23:59:52:5952"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-16 23:59:52:5952","version":"1.0.0"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-16 23:59:57:5957"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-16 23:59:57:5957","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:57:5957","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-16 23:59:57:5957"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/dev/create-user","timestamp":"2025-07-17 00:00:17:017"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 00:00:18:018"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 00:00:18:018","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"id":"7b8ce126-d521-4cb7-98bb-e9084a98de0a","level":"info","message":"Analysis result created successfully","status":"completed","timestamp":"2025-07-17 00:00:18:018","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 00:00:28:028"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 00:00:28:028","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"id":"dee5f67c-09f3-4ce6-b9a3-b611c28329f3","level":"info","message":"Analysis result created successfully","status":"completed","timestamp":"2025-07-17 00:00:28:028","userId":"13e63acd-44cd-4090-bb73-34bce0d45614"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-17 05:44:46:4446"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-17 05:44:46:4446"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-17 05:44:46:4446"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-17 05:44:46:4446","version":"1.0.0"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 05:45:54:4554","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 05:45:54:4554","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 05:45:54:4554","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 05:45:54:4554","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 05:45:54:4554","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 05:45:54:4554","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 05:45:54:4554","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 05:45:54:4554","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 05:59:04:594","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 05:59:04:594","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 05:59:04:594","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 05:59:04:594","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 05:59:04:594","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 05:59:04:594","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 05:59:04:594","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 05:59:04:594","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 05:59:08:598","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 05:59:08:598","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 05:59:08:598","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 05:59:08:598","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 05:59:08:598","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 05:59:08:598","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 05:59:09:599","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 05:59:09:599","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:00:08:08","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:00:08:08","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:00:08:08","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:00:08:08","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:00:08:08","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:00:08:08","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:00:08:08","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:00:08:08","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:00:09:09","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:00:09:09","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:00:09:09","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:00:09:09","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:00:09:09","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:00:09:09","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:00:09:09","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:00:09:09","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:00:09:09","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:00:09:09","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:00:09:09","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:00:09:09","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:00:09:09","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:00:09:09","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:00:09:09","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:00:09:09","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:00:12:012","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:00:12:012","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:00:12:012","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:00:12:012","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:00:12:012","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:00:12:012","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:00:12:012","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:00:12:012","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-17 06:00:12:012"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-17 06:00:12:012"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-17 06:00:13:013"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-17 06:00:13:013"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-17 06:00:14:014"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-17 06:00:14:014"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:00:16:016","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:00:16:016","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:00:16:016","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:00:16:016","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:00:17:017","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:00:17:017","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:00:17:017","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:00:17:017","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:00:19:019","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:00:19:019","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:00:19:019","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:00:19:019","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:00:19:019","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:00:19:019","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:00:19:019","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:00:19:019","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:00:33:033","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:00:33:033","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:00:33:033","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:00:33:033","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:00:33:033","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:00:33:033","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:00:33:033","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:00:33:033","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:00:41:041","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:00:41:041","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:00:41:041","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:00:41:041","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:00:41:041","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:00:41:041","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:00:41:041","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:00:41:041","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:16:44:1644","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:16:44:1644","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:16:44:1644","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:16:44:1644","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:16:44:1644","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:16:44:1644","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:16:44:1644","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:16:44:1644","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:16:46:1646","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:16:46:1646","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:16:46:1646","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:16:46:1646","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:16:56:1656","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:16:56:1656","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:16:56:1656","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:16:56:1656","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:16:56:1656","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:16:56:1656","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:16:56:1656","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:16:56:1656","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:17:27:1727","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:17:27:1727","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:17:27:1727","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:17:27:1727","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:17:57:1757","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:17:57:1757","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:17:57:1757","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:17:57:1757","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:18:27:1827","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:18:27:1827","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:18:27:1827","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:18:27:1827","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:18:57:1857","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:18:57:1857","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:18:57:1857","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:18:57:1857","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:19:27:1927","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:19:27:1927","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:19:27:1927","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:19:27:1927","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:19:57:1957","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:19:57:1957","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:19:57:1957","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:19:57:1957","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:20:48:2048","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:20:48:2048","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:20:48:2048","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:20:48:2048","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:21:24:2124","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:21:24:2124","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:21:24:2124","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:21:24:2124","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:21:26:2126","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:21:26:2126","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:21:26:2126","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:21:26:2126","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:21:57:2157","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:21:57:2157","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:21:57:2157","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:21:57:2157","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:22:27:2227","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:22:27:2227","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:22:27:2227","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:22:27:2227","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:23:48:2348","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:23:48:2348","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:23:48:2348","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:23:48:2348","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:24:48:2448","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:24:48:2448","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:24:48:2448","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:24:48:2448","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:25:48:2548","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:25:48:2548","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:25:48:2548","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:25:48:2548","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:26:47:2647","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:26:47:2647","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:26:47:2647","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:26:47:2647","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:26:57:2657","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:26:57:2657","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:26:57:2657","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:26:57:2657","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:27:26:2726","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:27:26:2726","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:27:26:2726","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:27:26:2726","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:27:56:2756","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:27:56:2756","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:27:56:2756","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:27:56:2756","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:28:27:2827","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:28:27:2827","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:28:27:2827","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:28:27:2827","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:28:57:2857","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:28:57:2857","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:28:57:2857","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:28:57:2857","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:29:48:2948","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:29:48:2948","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:29:48:2948","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:29:48:2948","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:30:48:3048","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:30:48:3048","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:30:48:3048","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:30:48:3048","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:31:48:3148","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:31:48:3148","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:31:48:3148","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:31:48:3148","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:32:48:3248","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:32:48:3248","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:32:48:3248","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:32:48:3248","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:33:48:3348","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:33:48:3348","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:33:48:3348","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:33:48:3348","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:34:48:3448","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:34:48:3448","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:34:48:3448","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:34:48:3448","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:35:48:3548","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:35:48:3548","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:35:48:3548","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:35:48:3548","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:36:48:3648","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:36:48:3648","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:36:48:3648","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:36:48:3648","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:37:48:3748","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:37:48:3748","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:37:48:3748","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:37:48:3748","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:38:48:3848","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:38:48:3848","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:38:48:3848","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:38:48:3848","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:39:48:3948","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:39:48:3948","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:39:48:3948","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:39:48:3948","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:40:48:4048","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:40:48:4048","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:40:48:4048","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:40:48:4048","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:41:48:4148","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:41:48:4148","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:41:48:4148","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:41:48:4148","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:42:48:4248","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:42:48:4248","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:42:48:4248","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:42:48:4248","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:42:59:4259","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:42:59:4259","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:42:59:4259","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:42:59:4259","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:43:00:430","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:43:00:430","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:43:00:430","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:43:00:430","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:43:00:430","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:43:00:430","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:43:00:430","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:43:00:430","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:43:06:436","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:43:06:436","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:43:06:436","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:43:06:436","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:43:06:436","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:43:06:436","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:43:06:436","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:43:06:436","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:48:35:4835","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:48:35:4835","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:48:35:4835","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:48:35:4835","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 06:48:35:4835","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 06:48:35:4835","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 06:48:35:4835","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 06:48:35:4835","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:03:54:354","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:03:54:354","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:03:54:354","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:03:54:354","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:03:54:354","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:03:54:354","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:03:54:354","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:03:54:354","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:11:25:1125","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:11:25:1125","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:11:25:1125","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:11:25:1125","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:11:25:1125","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:11:25:1125","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:11:25:1125","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:11:25:1125","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:11:31:1131","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:11:31:1131","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:11:31:1131","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:11:31:1131","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:11:31:1131","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:11:31:1131","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:11:31:1131","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:11:31:1131","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:17:19:1719","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:17:19:1719","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:17:19:1719","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:17:19:1719","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:17:19:1719","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:17:19:1719","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:17:19:1719","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:17:19:1719","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:17:42:1742","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:17:42:1742","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:17:42:1742","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:17:42:1742","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:17:42:1742","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:17:42:1742","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:17:42:1742","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:17:42:1742","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:18:47:1847","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:18:47:1847","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:18:47:1847","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:18:47:1847","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:18:47:1847","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:18:47:1847","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:18:47:1847","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:18:47:1847","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:42:13:4213","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:42:13:4213","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:42:13:4213","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:42:13:4213","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:42:13:4213","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:42:13:4213","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:42:13:4213","total":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:42:13:4213","totalAnalyses":0,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:42:22:4222","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:42:22:4222","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:42:22:4222","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:42:22:4222","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:42:22:4222","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:42:22:4222","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:42:22:4222","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:42:22:4222","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:43:27:4327","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:43:27:4327","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:43:27:4327","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:43:27:4327","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:43:28:4328","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:43:28:4328","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:43:28:4328","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:43:28:4328","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:43:33:4333","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:43:33:4333","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:43:33:4333","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:43:33:4333","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:43:37:4337","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:43:37:4337","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:43:37:4337","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:43:37:4337","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:43:39:4339","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:43:39:4339","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:43:39:4339","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:43:39:4339","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:43:47:4347","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:43:47:4347","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:43:47:4347","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:43:47:4347","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:43:49:4349","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:43:49:4349","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:43:49:4349","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:43:49:4349","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:43:53:4353","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:43:53:4353","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:43:53:4353","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:43:53:4353","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:48:17:4817","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:48:17:4817","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:48:17:4817","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:48:17:4817","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:48:17:4817","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:48:17:4817","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:48:17:4817","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:48:17:4817","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:48:24:4824","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:48:24:4824","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:48:24:4824","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:48:24:4824","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:48:24:4824","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:48:24:4824","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:48:24:4824","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:48:24:4824","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:48:31:4831","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:48:31:4831","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:48:31:4831","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:48:31:4831","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:48:39:4839","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:48:39:4839","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:48:39:4839","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:48:39:4839","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 07:48:39:4839","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 07:48:39:4839","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 07:48:39:4839","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 07:48:39:4839","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 08:01:33:133","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 08:01:33:133","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 08:01:34:134","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 08:01:34:134","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 08:01:34:134","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 08:01:34:134","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 08:01:34:134","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 08:01:34:134","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 08:02:26:226","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 08:02:26:226","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 08:02:26:226","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 08:02:26:226","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 08:02:26:226","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 08:02:26:226","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 08:02:26:226","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 08:02:26:226","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 08:02:29:229","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 08:02:29:229","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 08:02:29:229","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 08:02:29:229","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 08:02:29:229","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 08:02:29:229","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 08:02:29:229","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 08:02:29:229","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-17 08:31:33:3133"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-17 08:31:33:3133"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-17 08:31:33:3133"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-17 08:31:33:3133","version":"1.0.0"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-17 10:04:32:432"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-17 10:04:32:432"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-17 10:04:32:432"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-17 10:04:32:432","version":"1.0.0"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 10:15:26:1526","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 10:15:26:1526","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 10:15:26:1526","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 10:15:26:1526","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-17 10:34:57:3457"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-17 10:34:57:3457"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-17 10:34:57:3457"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-17 10:34:57:3457","version":"1.0.0"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 10:35:25:3525"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 10:35:25:3525","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:35:25:3525","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:35:25:3525"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 10:35:30:3530"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 10:35:30:3530","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:35:30:3530","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:35:30:3530"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 10:35:40:3540"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 10:35:40:3540","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:35:40:3540","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:35:40:3540"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 10:36:00:360"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 10:36:00:360","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:36:00:360","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:36:00:360"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 10:36:40:3640"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 10:36:40:3640","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:36:40:3640","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:36:40:3640"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 10:36:45:3645"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 10:36:45:3645","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:36:45:3645","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:36:45:3645"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 10:36:55:3655"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 10:36:55:3655","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:36:55:3655","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:36:55:3655"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 10:37:15:3715"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 10:37:15:3715","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:37:15:3715","userId":"a976e5bc-2074-42de-854b-84f52293233f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 10:37:15:3715"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-17 10:42:58:4258"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-17 10:42:58:4258"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-17 10:42:58:4258"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-17 10:42:58:4258","version":"1.0.0"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 10:44:36:4436","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 10:44:36:4436","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 10:44:36:4436","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 10:44:36:4436","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-17 10:44:36:4436","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-17 10:44:36:4436","total":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Fetching user statistics","timestamp":"2025-07-17 10:44:36:4436","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"User statistics fetched successfully","timestamp":"2025-07-17 10:44:36:4436","totalAnalyses":0,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-17 11:01:40:140"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-17 11:02:07:27"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-17 11:02:07:27"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-17 11:02:07:27"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-17 11:02:07:27","version":"1.0.0"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 11:03:05:35"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 11:03:05:35","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:03:05:35","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:03:05:35"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 11:03:10:310"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 11:03:10:310","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:03:10:310","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:03:10:310"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 11:03:20:320"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 11:03:20:320","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:03:20:320","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:03:20:320"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 11:03:40:340"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 11:03:40:340","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:03:40:340","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:03:40:340"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 11:04:05:45"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 11:04:05:45","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:04:05:45","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:04:05:45"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 11:04:10:410"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 11:04:10:410","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:04:10:410","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:04:10:410"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-17 11:13:16:1316"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-17 11:13:16:1316"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-17 11:13:16:1316"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-17 11:13:16:1316","version":"1.0.0"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 11:13:57:1357"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 11:13:57:1357","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:13:57:1357","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:13:57:1357"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 11:14:02:142"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 11:14:02:142","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:02:142","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:02:142"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 11:14:12:1412"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 11:14:12:1412","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:12:1412","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:12:1412"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 11:14:32:1432"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 11:14:32:1432","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:32:1432","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:32:1432"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 11:14:41:1441"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 11:14:41:1441","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:41:1441","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:41:1441"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 11:14:46:1446"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 11:14:46:1446","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:46:1446","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:46:1446"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 11:14:56:1456"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 11:14:56:1456","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:56:1456","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:14:56:1456"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 11:15:00:150"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 11:15:00:150","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:00:150","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:00:150"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 11:15:05:155"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 11:15:05:155","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:05:155","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:05:155"}
{"ip":"::1","level":"info","message":"Internal service request authenticated","method":"POST","path":"/results","timestamp":"2025-07-17 11:15:15:1515"}
{"level":"info","message":"Creating new analysis result","status":"completed","timestamp":"2025-07-17 11:15:15:1515","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","errorName":"SequelizeForeignKeyConstraintError","level":"error","message":"Failed to create analysis result","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:15:1515","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","ip":"::1","level":"error","message":"Error occurred","method":"POST","path":"/archive/results","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.insert (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async AnalysisResult.create (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async Object.createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\resultsService.js:22:20)\n    at async createResult (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\controllers\\resultsController.js:78:20)","timestamp":"2025-07-17 11:15:15:1515"}
