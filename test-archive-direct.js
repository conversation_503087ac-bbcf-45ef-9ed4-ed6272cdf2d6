/**
 * Test script untuk menguji Archive Service secara langsung
 */

const axios = require('axios');

// Test data yang sederhana
const testData = {
  user_id: "6784e258-1e23-4ee7-848c-a3e41e80710a",
  assessment_data: {
    riasec: {
      realistic: 75,
      investigative: 85,
      artistic: 60,
      social: 50,
      enterprising: 70,
      conventional: 55
    },
    ocean: {
      openness: 80,
      conscientiousness: 65,
      extraversion: 55,
      agreeableness: 45,
      neuroticism: 30
    },
    viaIs: {
      creativity: 85,
      curiosity: 78,
      judgment: 70,
      loveOfLearning: 65,
      perspective: 75,
      bravery: 60,
      perseverance: 80,
      honesty: 90,
      zest: 70,
      love: 85,
      kindness: 88,
      socialIntelligence: 75,
      teamwork: 82,
      fairness: 85,
      leadership: 70,
      forgiveness: 75,
      humility: 65,
      prudence: 70,
      selfRegulation: 75,
      appreciationOfBeauty: 80,
      gratitude: 85,
      hope: 80,
      humor: 75,
      spirituality: 60
    }
  },
  persona_profile: {
    archetype: "The Analytical Innovator",
    shortSummary: "Test persona profile for debugging",
    strengths: ["Analytical thinking", "Problem solving", "Creative innovation"],
    weaknesses: ["Perfectionism", "Overthinking", "Impatience"],
    careerRecommendation: [
      {
        careerName: "Data Scientist",
        careerProspect: {
          jobAvailability: "high",
          salaryPotential: "high",
          careerProgression: "high",
          industryGrowth: "super high",
          skillDevelopment: "high"
        }
      },
      {
        careerName: "Software Engineer",
        careerProspect: {
          jobAvailability: "high",
          salaryPotential: "high",
          careerProgression: "high",
          industryGrowth: "high",
          skillDevelopment: "high"
        }
      },
      {
        careerName: "Research Analyst",
        careerProspect: {
          jobAvailability: "moderate",
          salaryPotential: "moderate",
          careerProgression: "moderate",
          industryGrowth: "moderate",
          skillDevelopment: "high"
        }
      }
    ],
    insights: ["Focus on practical applications", "Develop communication skills", "Build leadership capabilities"],
    workEnvironment: "Collaborative and innovative environment",
    roleModel: ["Marie Curie", "Albert Einstein", "Steve Jobs", "Katherine Johnson"]
  },
  status: "completed"
};

async function testArchiveService() {
  try {
    console.log('🧪 Testing Archive Service directly...');
    console.log('📊 Test Data:', {
      user_id: testData.user_id,
      assessment_data_keys: Object.keys(testData.assessment_data),
      persona_profile_archetype: testData.persona_profile.archetype,
      status: testData.status
    });

    // Send request to archive service
    const response = await axios.post('http://localhost:3002/archive/results', testData, {
      headers: {
        'Content-Type': 'application/json',
        'X-Internal-Service': 'true',
        'X-Service-Key': 'internal_service_secret_key_change_in_production'
      }
    });

    console.log('✅ Archive request sent successfully');
    console.log('📝 Response:', response.data);

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Headers:', error.response.headers);
    }
  }
}

// Run test
testArchiveService();
