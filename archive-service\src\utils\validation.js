/**
 * Validation Schemas using Joi
 */

const Jo<PERSON> = require('joi');

// UUID validation pattern
const uuidSchema = Joi.string().uuid().required();

// Assessment data schema
const assessmentDataSchema = Joi.object({
  riasec: Joi.object({
    realistic: Joi.number().integer().min(0).max(100).required(),
    investigative: Joi.number().integer().min(0).max(100).required(),
    artistic: Joi.number().integer().min(0).max(100).required(),
    social: Joi.number().integer().min(0).max(100).required(),
    enterprising: Joi.number().integer().min(0).max(100).required(),
    conventional: Joi.number().integer().min(0).max(100).required()
  }).required(),
  ocean: Joi.object({
    openness: Joi.number().integer().min(0).max(100).required(),
    conscientiousness: Joi.number().integer().min(0).max(100).required(),
    extraversion: Joi.number().integer().min(0).max(100).required(),
    agreeableness: Joi.number().integer().min(0).max(100).required(),
    neuroticism: Joi.number().integer().min(0).max(100).required()
  }).required(),
  viaIs: Joi.object({
    creativity: Joi.number().integer().min(0).max(100).required(),
    curiosity: Joi.number().integer().min(0).max(100).required(),
    judgment: Joi.number().integer().min(0).max(100).required(),
    loveOfLearning: Joi.number().integer().min(0).max(100).required(),
    perspective: Joi.number().integer().min(0).max(100).required(),
    bravery: Joi.number().integer().min(0).max(100).required(),
    perseverance: Joi.number().integer().min(0).max(100).required(),
    honesty: Joi.number().integer().min(0).max(100).required(),
    zest: Joi.number().integer().min(0).max(100).required(),
    love: Joi.number().integer().min(0).max(100).required(),
    kindness: Joi.number().integer().min(0).max(100).required(),
    socialIntelligence: Joi.number().integer().min(0).max(100).required(),
    teamwork: Joi.number().integer().min(0).max(100).required(),
    fairness: Joi.number().integer().min(0).max(100).required(),
    leadership: Joi.number().integer().min(0).max(100).required(),
    forgiveness: Joi.number().integer().min(0).max(100).required(),
    humility: Joi.number().integer().min(0).max(100).required(),
    prudence: Joi.number().integer().min(0).max(100).required(),
    selfRegulation: Joi.number().integer().min(0).max(100).required(),
    appreciationOfBeauty: Joi.number().integer().min(0).max(100).required(),
    gratitude: Joi.number().integer().min(0).max(100).required(),
    hope: Joi.number().integer().min(0).max(100).required(),
    humor: Joi.number().integer().min(0).max(100).required(),
    spirituality: Joi.number().integer().min(0).max(100).required()
  }).required(),
  multipleIntelligences: Joi.object({
    linguistic: Joi.number().min(0).max(100),
    logicalMathematical: Joi.number().min(0).max(100),
    spatial: Joi.number().min(0).max(100),
    bodilyKinesthetic: Joi.number().min(0).max(100),
    musical: Joi.number().min(0).max(100),
    interpersonal: Joi.number().min(0).max(100),
    intrapersonal: Joi.number().min(0).max(100),
    naturalistic: Joi.number().min(0).max(100)
  }),
  cognitiveStyleIndex: Joi.object({
    analytic: Joi.number().min(0).max(100),
    intuitive: Joi.number().min(0).max(100)
  })
}).unknown(true);

// Career recommendation schema
const careerRecommendationSchema = Joi.object({
  careerName: Joi.string().required(),
  careerProspect: Joi.object({
    jobAvailability: Joi.string().valid('super high', 'high', 'moderate', 'low', 'super low').required(),
    salaryPotential: Joi.string().valid('super high', 'high', 'moderate', 'low', 'super low').required(),
    careerProgression: Joi.string().valid('super high', 'high', 'moderate', 'low', 'super low').required(),
    industryGrowth: Joi.string().valid('super high', 'high', 'moderate', 'low', 'super low').required(),
    skillDevelopment: Joi.string().valid('super high', 'high', 'moderate', 'low', 'super low').required()
  }).required()
});

// Persona profile schema
const personaProfileSchema = Joi.object({
  archetype: Joi.string().required(),
  shortSummary: Joi.string().required(),
  strengths: Joi.array().items(Joi.string()).min(3).max(5).required(),
  weaknesses: Joi.array().items(Joi.string()).min(3).max(5).required(),
  careerRecommendation: Joi.array().items(careerRecommendationSchema).min(3).max(5).required(),
  insights: Joi.array().items(Joi.string()).min(3).max(5).required(),
  workEnvironment: Joi.string().required(),
  roleModel: Joi.array().items(Joi.string()).min(4).max(5).required()
}).required();

// Create analysis result schema
const createAnalysisResultSchema = Joi.object({
  user_id: uuidSchema,
  assessment_data: assessmentDataSchema.optional(),
  persona_profile: personaProfileSchema,
  status: Joi.string().valid('completed', 'processing', 'failed').default('completed')
});

// Update analysis result schema
const updateAnalysisResultSchema = Joi.object({
  persona_profile: personaProfileSchema.optional(),
  status: Joi.string().valid('completed', 'processing', 'failed').optional()
}).min(1);

// Query parameters schema for list results
const listResultsQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
  status: Joi.string().valid('completed', 'processing', 'failed').optional(),
  sort: Joi.string().valid('created_at', 'updated_at').default('created_at'),
  order: Joi.string().valid('asc', 'desc', 'ASC', 'DESC').default('desc')
});

// UUID parameter schema
const uuidParamSchema = Joi.object({
  id: uuidSchema
});

module.exports = {
  createAnalysisResultSchema,
  updateAnalysisResultSchema,
  listResultsQuerySchema,
  uuidParamSchema,
  assessmentDataSchema,
  personaProfileSchema
};
