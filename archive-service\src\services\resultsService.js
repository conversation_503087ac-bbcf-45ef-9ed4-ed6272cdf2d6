/**
 * Results Service
 * Business logic for analysis results management
 */

const AnalysisResult = require('../models/AnalysisResult');
const { NotFoundError, ForbiddenError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

/**
 * Create a new analysis result
 * @param {Object} data - Analysis result data
 * @returns {Promise<Object>} - Created result
 */
const createResult = async (data) => {
  try {
    logger.info('Creating new analysis result', {
      userId: data.user_id,
      status: data.status,
      dataKeys: Object.keys(data),
      assessmentDataType: typeof data.assessment_data,
      personaProfileType: typeof data.persona_profile
    });

    // Log the actual data being sent for debugging
    logger.debug('Full data being inserted', {
      data: JSON.stringify(data, null, 2)
    });

    let result;
    try {
      result = await AnalysisResult.create(data);
    } catch (dbError) {
      logger.error('Database insert error details:', {
        name: dbError.name,
        message: dbError.message,
        sql: dbError.sql,
        parameters: dbError.parameters,
        original: dbError.original ? {
          message: dbError.original.message,
          code: dbError.original.code,
          detail: dbError.original.detail,
          hint: dbError.original.hint,
          position: dbError.original.position,
          constraint: dbError.original.constraint,
          table: dbError.original.table,
          column: dbError.original.column
        } : null,
        stack: dbError.stack
      });
      throw dbError;
    }

    logger.info('Analysis result created successfully', {
      id: result.id,
      userId: result.user_id,
      status: result.status
    });

    return result;
  } catch (error) {
    logger.error('Failed to create analysis result', {
      error: error.message,
      errorName: error.name,
      errorCode: error.code,
      constraint: error.constraint,
      detail: error.detail,
      userId: data.user_id,
      stack: error.stack
    });

    // If it's a foreign key constraint error for user_id, create the user in development mode
    if (error.name === 'SequelizeForeignKeyConstraintError' &&
        error.constraint === 'fk_analysis_results_user_id' &&
        process.env.NODE_ENV === 'development') {

      logger.warn('User not found, creating user for development mode', {
        userId: data.user_id
      });

      try {
        // Create user in auth.users table
        await AnalysisResult.sequelize.query(`
          INSERT INTO auth.users (id, email, password_hash, token_balance)
          VALUES (:userId, :email, :passwordHash, :tokenBalance)
          ON CONFLICT (id) DO NOTHING
        `, {
          replacements: {
            userId: data.user_id,
            email: `dev-user-${data.user_id}@example.com`,
            passwordHash: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.G',
            tokenBalance: 100
          }
        });

        logger.info('Development user created, retrying analysis result creation', {
          userId: data.user_id
        });

        // Retry creating the analysis result
        const result = await AnalysisResult.create(data);

        logger.info('Analysis result created successfully after user creation', {
          id: result.id,
          userId: result.user_id,
          status: result.status
        });

        return result;
      } catch (retryError) {
        logger.error('Failed to create user or retry analysis result creation', {
          error: retryError.message,
          userId: data.user_id
        });
        throw retryError;
      }
    }

    throw error;
  }
};

/**
 * Get analysis results for a user with pagination
 * @param {String} userId - User ID
 * @param {Object} options - Query options
 * @returns {Promise<Object>} - Results with pagination
 */
const getResultsByUser = async (userId, options = {}) => {
  try {
    logger.info('Fetching analysis results for user', {
      userId,
      options
    });
    
    const results = await AnalysisResult.findByUserWithPagination(userId, options);
    
    logger.info('Analysis results fetched successfully', {
      userId,
      count: results.results.length,
      total: results.pagination.total
    });
    
    return results;
  } catch (error) {
    logger.error('Failed to fetch analysis results', {
      error: error.message,
      userId
    });
    throw error;
  }
};

/**
 * Get a specific analysis result by ID
 * @param {String} resultId - Result ID
 * @param {String} userId - User ID (for access control)
 * @param {Boolean} isInternalService - Whether request is from internal service
 * @returns {Promise<Object>} - Analysis result
 */
const getResultById = async (resultId, userId = null, isInternalService = false) => {
  try {
    logger.info('Fetching analysis result by ID', {
      resultId,
      userId,
      isInternalService
    });
    
    const result = await AnalysisResult.findByPk(resultId);
    
    if (!result) {
      throw new NotFoundError('Analysis result not found');
    }
    
    // Check access permissions (skip for internal services)
    if (!isInternalService && userId && result.user_id !== userId) {
      throw new ForbiddenError('You do not have access to this analysis result');
    }
    
    logger.info('Analysis result fetched successfully', {
      resultId,
      userId: result.user_id
    });
    
    return result;
  } catch (error) {
    logger.error('Failed to fetch analysis result', {
      error: error.message,
      resultId,
      userId
    });
    throw error;
  }
};

/**
 * Update an analysis result
 * @param {String} resultId - Result ID
 * @param {Object} updateData - Update data
 * @param {String} userId - User ID (for access control)
 * @param {Boolean} isInternalService - Whether request is from internal service
 * @returns {Promise<Object>} - Updated result
 */
const updateResult = async (resultId, updateData, userId = null, isInternalService = false) => {
  try {
    logger.info('Updating analysis result', {
      resultId,
      userId,
      isInternalService,
      updateFields: Object.keys(updateData)
    });
    
    const result = await AnalysisResult.findByPk(resultId);
    
    if (!result) {
      throw new NotFoundError('Analysis result not found');
    }
    
    // Check access permissions (skip for internal services)
    if (!isInternalService && userId && result.user_id !== userId) {
      throw new ForbiddenError('You do not have access to this analysis result');
    }
    
    // Update the result
    await result.update(updateData);
    
    logger.info('Analysis result updated successfully', {
      resultId,
      userId: result.user_id
    });
    
    return result;
  } catch (error) {
    logger.error('Failed to update analysis result', {
      error: error.message,
      resultId,
      userId
    });
    throw error;
  }
};

/**
 * Delete an analysis result
 * @param {String} resultId - Result ID
 * @param {String} userId - User ID (for access control)
 * @returns {Promise<void>}
 */
const deleteResult = async (resultId, userId) => {
  try {
    logger.info('Deleting analysis result', {
      resultId,
      userId
    });
    
    const result = await AnalysisResult.findByPk(resultId);
    
    if (!result) {
      throw new NotFoundError('Analysis result not found');
    }
    
    // Check access permissions
    if (result.user_id !== userId) {
      throw new ForbiddenError('You do not have access to this analysis result');
    }
    
    // Delete the result
    await result.destroy();
    
    logger.info('Analysis result deleted successfully', {
      resultId,
      userId
    });
  } catch (error) {
    logger.error('Failed to delete analysis result', {
      error: error.message,
      resultId,
      userId
    });
    throw error;
  }
};

module.exports = {
  createResult,
  getResultsByUser,
  getResultById,
  updateResult,
  deleteResult
};
