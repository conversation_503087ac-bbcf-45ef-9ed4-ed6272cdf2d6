/**
 * Script untuk mengecek dan setup database
 */

const { Sequelize } = require('./archive-service/node_modules/sequelize');

// Database configuration
const sequelize = new Sequelize('atma_db', 'atma_user', 'secret-passworrd', {
  host: 'localhost',
  port: 5432,
  dialect: 'postgres',
  logging: console.log
});

async function checkDatabase() {
  try {
    console.log('🔍 Checking database connection...');

    // Test connection
    await sequelize.authenticate();
    console.log('✅ Database connection successful');
    
    // Check if archive schema exists
    const [schemaResult] = await sequelize.query(`
      SELECT schema_name
      FROM information_schema.schemata
      WHERE schema_name = 'archive'
    `);

    if (schemaResult.length === 0) {
      console.log('📝 Creating archive schema...');
      await sequelize.query('CREATE SCHEMA IF NOT EXISTS archive');
      console.log('✅ Archive schema created');
    } else {
      console.log('✅ Archive schema exists');
    }
    
    // Check if analysis_results table exists
    const [tableResult] = await sequelize.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'archive'
      AND table_name = 'analysis_results'
    `);

    if (tableResult.length === 0) {
      console.log('📝 Creating analysis_results table...');
      
      const createTableSQL = `
        CREATE TABLE archive.analysis_results (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID NOT NULL,
          assessment_data JSONB NOT NULL,
          persona_profile JSONB NOT NULL,
          status VARCHAR(50) NOT NULL DEFAULT 'completed',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
      `;
      
      await sequelize.query(createTableSQL);
      console.log('✅ analysis_results table created');

      // Create indexes
      await sequelize.query(`
        CREATE INDEX IF NOT EXISTS idx_analysis_results_user_id
        ON archive.analysis_results(user_id);
      `);

      await sequelize.query(`
        CREATE INDEX IF NOT EXISTS idx_analysis_results_status
        ON archive.analysis_results(status);
      `);

      await sequelize.query(`
        CREATE INDEX IF NOT EXISTS idx_analysis_results_created_at
        ON archive.analysis_results(created_at);
      `);
      
      console.log('✅ Indexes created');
      
    } else {
      console.log('✅ analysis_results table exists');
    }
    
    // Check table structure
    const [columnsResult] = await sequelize.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_schema = 'archive'
      AND table_name = 'analysis_results'
      ORDER BY ordinal_position
    `);

    console.log('📋 Table structure:');
    columnsResult.forEach(row => {
      console.log(`  - ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });

    console.log('🎉 Database check completed successfully');

  } catch (error) {
    console.error('❌ Database check failed:', error.message);
    console.error('Error details:', error);
  } finally {
    await sequelize.close();
  }
}

// Run the check
checkDatabase();
