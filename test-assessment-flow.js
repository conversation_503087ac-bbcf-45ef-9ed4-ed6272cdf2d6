/**
 * Test script untuk men<PERSON>ji end-to-end flow assessment
 */

const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

// Test data dengan format yang valid
const testAssessmentData = {
  riasec: {
    realistic: 75,
    investigative: 85,
    artistic: 60,
    social: 50,
    enterprising: 70,
    conventional: 55
  },
  ocean: {
    openness: 80,
    conscientiousness: 65,
    extraversion: 55,
    agreeableness: 45,
    neuroticism: 30
  },
  viaIs: {
    creativity: 85,
    curiosity: 90,
    judgment: 75,
    loveOfLearning: 80,
    perspective: 70,
    bravery: 65,
    perseverance: 85,
    honesty: 90,
    zest: 75,
    love: 80,
    kindness: 85,
    socialIntelligence: 70,
    teamwork: 80,
    fairness: 85,
    leadership: 75,
    forgiveness: 70,
    humility: 65,
    prudence: 75,
    selfRegulation: 80,
    appreciationOfBeauty: 70,
    gratitude: 85,
    hope: 80,
    humor: 75,
    spirituality: 60
  }
};

async function testAssessmentFlow() {
  try {
    console.log('🧪 Testing Assessment Flow...');
    console.log('📊 Test Data:', {
      riasec: Object.keys(testAssessmentData.riasec).length + ' dimensions',
      ocean: Object.keys(testAssessmentData.ocean).length + ' dimensions',
      viaIs: Object.keys(testAssessmentData.viaIs).length + ' strengths'
    });

    // Send request to assessment service test endpoint (no auth required)
    const response = await axios.post('http://localhost:3003/test/submit', testAssessmentData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Assessment request sent successfully');
    console.log('📝 Response:', response.data);

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Run test
testAssessmentFlow();
