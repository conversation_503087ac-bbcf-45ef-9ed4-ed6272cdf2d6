# ATMA Backend End-to-End Test Script
# Tests the complete flow: Assessment submission -> Analysis -> Database storage -> Notification

param(
    [switch]$SkipServiceCheck,
    [switch]$Verbose,
    [string]$TestUser = "<EMAIL>",
    [string]$TestPassword = "password123"
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Cyan"

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Red
}

# Service endpoints
$services = @{
    "API Gateway" = "http://localhost:3000"
    "Auth Service" = "http://localhost:3001"
    "Archive Service" = "http://localhost:3002"
    "Assessment Service" = "http://localhost:3003"
    "Notification Service" = "http://localhost:3005"
}

# Test data
$testAssessmentData = @{
    riasec = @{
        realistic = 75
        investigative = 85
        artistic = 60
        social = 50
        enterprising = 70
        conventional = 55
    }
    ocean = @{
        openness = 80
        conscientiousness = 65
        extraversion = 55
        agreeableness = 45
        neuroticism = 30
    }
    viaIs = @{
        creativity = 85
        curiosity = 78
        judgment = 70
        loveOfLearning = 65
        perspective = 75
        bravery = 60
        perseverance = 80
        honesty = 90
        zest = 70
        love = 85
        kindness = 88
        socialIntelligence = 75
        teamwork = 82
        fairness = 85
        leadership = 70
        forgiveness = 75
        humility = 65
        prudence = 70
        selfRegulation = 75
        appreciationOfBeauty = 80
        gratitude = 85
        hope = 80
        humor = 75
        spirituality = 60
    }
}

function Test-ServiceHealth {
    param([string]$ServiceName, [string]$Url)
    
    try {
        $response = Invoke-RestMethod -Uri "$Url/health" -Method GET -TimeoutSec 5
        if ($response.status -eq "healthy" -or $response.success -eq $true) {
            Write-Success "$ServiceName is healthy"
            return $true
        } else {
            Write-Warning "$ServiceName responded but status is not healthy"
            return $false
        }
    } catch {
        Write-Error "$ServiceName is not responding: $($_.Exception.Message)"
        return $false
    }
}

function Test-AllServices {
    Write-Status "Checking service health..."
    $allHealthy = $true
    
    foreach ($service in $services.GetEnumerator()) {
        $healthy = Test-ServiceHealth -ServiceName $service.Key -Url $service.Value
        if (-not $healthy) {
            $allHealthy = $false
        }
    }
    
    return $allHealthy
}

function Register-TestUser {
    Write-Status "Registering test user..."
    
    $registerData = @{
        email = $TestUser
        password = $TestPassword
    } | ConvertTo-Json
    
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:3001/auth/register" -Method POST -Body $registerData -ContentType "application/json"
        Write-Success "Test user registered successfully"
        return $response
    } catch {
        if ($_.Exception.Response.StatusCode -eq 409) {
            Write-Warning "Test user already exists, proceeding with login"
            return $null
        } else {
            Write-Error "Failed to register test user: $($_.Exception.Message)"
            throw
        }
    }
}

function Login-TestUser {
    Write-Status "Logging in test user..."
    
    $loginData = @{
        email = $TestUser
        password = $TestPassword
    } | ConvertTo-Json
    
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:3001/auth/login" -Method POST -Body $loginData -ContentType "application/json"
        Write-Success "Test user logged in successfully"
        return $response.data.token
    } catch {
        Write-Error "Failed to login test user: $($_.Exception.Message)"
        throw
    }
}

function Submit-Assessment {
    param([string]$Token)
    
    Write-Status "Submitting assessment data..."
    
    $headers = @{
        "Authorization" = "Bearer $Token"
        "Content-Type" = "application/json"
    }
    
    $assessmentJson = $testAssessmentData | ConvertTo-Json -Depth 3
    
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:3003/assessments/submit" -Method POST -Body $assessmentJson -Headers $headers
        Write-Success "Assessment submitted successfully"
        Write-Status "Job ID: $($response.data.jobId)"
        Write-Status "Queue Position: $($response.data.queuePosition)"
        Write-Status "Remaining Tokens: $($response.data.remainingTokens)"
        return $response.data
    } catch {
        Write-Error "Failed to submit assessment: $($_.Exception.Message)"
        if ($_.Exception.Response) {
            $errorResponse = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorResponse)
            $errorBody = $reader.ReadToEnd()
            Write-Error "Error details: $errorBody"
        }
        throw
    }
}

function Wait-ForAnalysis {
    param([string]$JobId, [int]$TimeoutMinutes = 10)
    
    Write-Status "Waiting for analysis to complete (timeout: $TimeoutMinutes minutes)..."
    
    $timeout = (Get-Date).AddMinutes($TimeoutMinutes)
    $checkInterval = 10 # seconds
    
    while ((Get-Date) -lt $timeout) {
        try {
            # Check if result exists in archive service
            $response = Invoke-RestMethod -Uri "http://localhost:3002/archive/results" -Method GET -Headers @{
                "X-Internal-Service" = "true"
                "X-Service-Key" = "internal_service_secret_key_change_in_production"
            }
            
            # Look for our job result
            $result = $response.data | Where-Object { $_.id -eq $JobId -or $_.user_id -eq $JobId }
            
            if ($result) {
                Write-Success "Analysis completed! Result found in archive."
                return $result
            }
            
            Write-Status "Analysis still in progress... checking again in $checkInterval seconds"
            Start-Sleep -Seconds $checkInterval
            
        } catch {
            Write-Warning "Error checking analysis status: $($_.Exception.Message)"
            Start-Sleep -Seconds $checkInterval
        }
    }
    
    Write-Error "Analysis did not complete within $TimeoutMinutes minutes"
    return $null
}

function Verify-DatabaseStorage {
    param([string]$UserId)
    
    Write-Status "Verifying database storage..."
    
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:3002/archive/results" -Method GET -Headers @{
            "X-Internal-Service" = "true"
            "X-Service-Key" = "internal_service_secret_key_change_in_production"
        }
        
        $userResults = $response.data | Where-Object { $_.user_id -eq $UserId }
        
        if ($userResults) {
            Write-Success "Database storage verified - found $($userResults.Count) result(s) for user"
            return $userResults
        } else {
            Write-Error "No results found in database for user $UserId"
            return $null
        }
    } catch {
        Write-Error "Failed to verify database storage: $($_.Exception.Message)"
        throw
    }
}

function Test-NotificationService {
    Write-Status "Testing notification service..."
    
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:3005/notifications/status" -Method GET -Headers @{
            "X-Internal-Service" = "true"
            "X-Service-Key" = "internal_service_secret_key_change_in_production"
        }
        
        Write-Success "Notification service is operational"
        Write-Status "Active connections: $($response.connections)"
        return $true
    } catch {
        Write-Error "Failed to test notification service: $($_.Exception.Message)"
        return $false
    }
}

# Main execution
try {
    Write-Status "Starting ATMA Backend End-to-End Test"
    Write-Status "=================================="
    
    # Step 1: Check service health
    if (-not $SkipServiceCheck) {
        $servicesHealthy = Test-AllServices
        if (-not $servicesHealthy) {
            Write-Error "Some services are not healthy. Use -SkipServiceCheck to proceed anyway."
            exit 1
        }
    }
    
    # Step 2: Setup test user
    Register-TestUser
    $token = Login-TestUser
    
    # Step 3: Submit assessment
    $submissionResult = Submit-Assessment -Token $token
    $jobId = $submissionResult.jobId
    
    # Step 4: Wait for analysis
    $analysisResult = Wait-ForAnalysis -JobId $jobId
    
    if ($analysisResult) {
        # Step 5: Verify database storage
        $dbResults = Verify-DatabaseStorage -UserId $analysisResult.user_id
        
        # Step 6: Test notification service
        $notificationOk = Test-NotificationService
        
        # Summary
        Write-Status ""
        Write-Status "End-to-End Test Summary"
        Write-Status "======================="
        Write-Success "✓ Assessment submission"
        Write-Success "✓ Token deduction"
        Write-Success "✓ Queue processing"
        Write-Success "✓ Analysis completion"
        Write-Success "✓ Database storage"
        
        if ($notificationOk) {
            Write-Success "✓ Notification service"
        } else {
            Write-Warning "⚠ Notification service issues"
        }
        
        Write-Success ""
        Write-Success "🎉 End-to-End test completed successfully!"

    } else {
        Write-Error "Analysis did not complete - test failed"
        exit 1
    }

} catch {
    Write-Error "End-to-End test failed: $($_.Exception.Message)"
    exit 1
}
