/**
 * <PERSON>ript untuk membuat test user di database
 */

const { Sequelize } = require('./archive-service/node_modules/sequelize');

// Database configuration
const sequelize = new Sequelize('atma_db', 'atma_user', 'secret-passworrd', {
  host: 'localhost',
  port: 5432,
  dialect: 'postgres',
  logging: console.log
});

const TEST_USER_ID = 'e9034651-8aa9-471b-97c7-71babc1ac589';
const TEST_EMAIL = '<EMAIL>';

async function createTestUser() {
  try {
    console.log('🔍 Checking for existing test user...');
    
    // Test connection
    await sequelize.authenticate();
    console.log('✅ Database connection successful');
    
    // Check if user exists
    const [userResult] = await sequelize.query(`
      SELECT id, email
      FROM auth.users
      WHERE id = '${TEST_USER_ID}'
    `);

    if (userResult.length > 0) {
      console.log('✅ Test user already exists:', userResult[0]);
      return userResult[0];
    }
    
    // Check table structure first
    const [columnsResult] = await sequelize.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_schema = 'auth'
      AND table_name = 'users'
      ORDER BY ordinal_position
    `);

    console.log('📋 Users table structure:');
    columnsResult.forEach(row => {
      console.log(`  - ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });

    console.log('📝 Creating test user...');

    // Create test user with only basic fields including token_balance
    const [insertResult] = await sequelize.query(`
      INSERT INTO auth.users (id, email, password_hash, token_balance, created_at, updated_at)
      VALUES (
        '${TEST_USER_ID}',
        '<EMAIL>',
        '$2b$10$example.hash.for.testing.purposes.only',
        100,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
      )
      RETURNING id, email, token_balance
    `);
    
    console.log('✅ Test user created successfully:', insertResult[0]);
    return insertResult[0];
    
  } catch (error) {
    console.error('❌ Failed to create test user:', error.message);
    
    if (error.message.includes('relation "auth.users" does not exist')) {
      console.log('📝 Creating auth schema and users table...');
      
      try {
        // Create auth schema
        await sequelize.query('CREATE SCHEMA IF NOT EXISTS auth');
        
        // Create users table
        const createUsersTableSQL = `
          CREATE TABLE IF NOT EXISTS auth.users (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            email VARCHAR(255) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            tokens INTEGER DEFAULT 0,
            is_verified BOOLEAN DEFAULT false,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
          );
        `;
        
        await sequelize.query(createUsersTableSQL);
        console.log('✅ Users table created');
        
        // Create indexes
        await sequelize.query(`
          CREATE INDEX IF NOT EXISTS idx_users_email 
          ON auth.users(email);
        `);
        
        // Now create the test user
        const [insertResult] = await sequelize.query(`
          INSERT INTO auth.users (id, email, password_hash, is_verified, created_at, updated_at)
          VALUES (
            '${TEST_USER_ID}',
            '${TEST_EMAIL}',
            '$2b$10$example.hash.for.testing.purposes.only',
            true,
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
          )
          RETURNING id, email
        `);
        
        console.log('✅ Test user created successfully:', insertResult[0]);
        return insertResult[0];
        
      } catch (createError) {
        console.error('❌ Failed to create schema/table:', createError.message);
        throw createError;
      }
    } else {
      throw error;
    }
  } finally {
    await sequelize.close();
  }
}

// Run the script
createTestUser();
